package org.example.test3;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 流程图布局处理器
 * 实现智能的流程图自动布局系统，包括层级计算和坐标优化
 */
public class FlowLayoutProcessor {

    // 布局参数配置
    private static final int NODE_WIDTH = 180;         // 节点宽度
    private static final int NODE_HEIGHT = 110;        // 节点高度


    private ObjectMapper objectMapper;
    private Map<String, FlowNode> nodes;
    private Map<String, FlowConnection> connections;
    private Map<String, List<String>> adjacencyList;
    private Map<String, Integer> inDegree;

    public FlowLayoutProcessor() {
        this.objectMapper = new ObjectMapper();
        this.nodes = new HashMap<>();
        this.connections = new HashMap<>();
        this.adjacencyList = new HashMap<>();
        this.inDegree = new HashMap<>();
    }

    /**
     * 处理流程图布局的主方法
     * @param inputFilePath 输入JSON文件路径
     * @param outputFilePath 输出JSON文件路径
     */
    public void processFlowLayout(String inputFilePath, String outputFilePath) {
        try {
            System.out.println("🚀 开始处理流程图布局...");

            // 第一阶段：JSON文件结构分析
            System.out.println("\n📋 第一阶段：JSON文件结构分析");
            JsonNode rootNode = parseJsonFile(inputFilePath);
            analyzeJsonStructure(rootNode);

            // 第二阶段：图结构构建和层级计算
            System.out.println("\n🔗 第二阶段：图结构构建和层级计算");
            buildGraphStructure(rootNode);
            calculateNodeLevels();

            // 第三阶段：坐标计算和布局优化
            System.out.println("\n📐 第三阶段：坐标计算和布局优化");
            calculateNodeCoordinates();

            // 第四阶段：JSON文件更新和输出
            System.out.println("\n💾 第四阶段：JSON文件更新和输出");
            updateJsonWithNewLayout(rootNode, outputFilePath);

            System.out.println("\n✅ 流程图布局处理完成！");
            printLayoutStatistics();

        } catch (Exception e) {
            System.err.println("❌ 处理流程图布局时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 解析JSON文件
     */
    private JsonNode parseJsonFile(String filePath) throws IOException {
        System.out.println("   📖 正在解析JSON文件: " + filePath);
        File file = new File(filePath);
        if (!file.exists()) {
            throw new IOException("文件不存在: " + filePath);
        }

        JsonNode rootNode = objectMapper.readTree(file);
        System.out.println("   ✅ JSON文件解析成功");
        return rootNode;
    }

    /**
     * 分析JSON结构
     */
    private void analyzeJsonStructure(JsonNode rootNode) {
        System.out.println("   🔍 正在分析JSON结构...");

        int totalObjects = 0;
        int nodeObjects = 0;
        int connectionObjects = 0;
        int otherObjects = 0;

        Map<Integer, Integer> nodeTypeCount = new HashMap<>();

        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            totalObjects++;

            if (isNodeObject(value)) {
                nodeObjects++;
                JsonNode nodeTypeNode = value.get("nodeType");
                if (nodeTypeNode != null) {
                    int nodeType = nodeTypeNode.asInt();
                    nodeTypeCount.put(nodeType, nodeTypeCount.getOrDefault(nodeType, 0) + 1);
                }
            } else if (isConnectionObject(value)) {
                connectionObjects++;
            } else {
                otherObjects++;
            }
        }

        System.out.println("   📊 JSON结构分析结果:");
        System.out.println("      - 总对象数: " + totalObjects);
        System.out.println("      - 节点对象: " + nodeObjects);
        System.out.println("      - 连线对象: " + connectionObjects);
        System.out.println("      - 其他对象: " + otherObjects);

        System.out.println("   📋 节点类型分布:");
        nodeTypeCount.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> {
                String typeName = getNodeTypeName(entry.getKey());
                System.out.println(String.format("      - %s (类型%d): %d个",
                    typeName, entry.getKey(), entry.getValue()));
            });
    }

    /**
     * 构建图结构
     */
    private void buildGraphStructure(JsonNode rootNode) {
        System.out.println("   🏗️ 正在构建图结构...");

        // 第一遍：收集所有节点
        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();

            if (isNodeObject(value)) {
                FlowNode node = parseFlowNode(key, value);
                nodes.put(key, node);
                adjacencyList.put(key, new ArrayList<>());
                inDegree.put(key, 0);
            }
        }

        // 第二遍：收集所有连线并构建邻接表
        fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> entry = fields.next();
            String key = entry.getKey();
            JsonNode value = entry.getValue();

            if (isConnectionObject(value)) {
                FlowConnection connection = parseFlowConnection(key, value);
                if (connection != null && connection.isValidConnection()) {
                    connections.put(key, connection);

                    String sourceId = connection.getSourceId();
                    String targetId = connection.getTargetId();

                    // 不排除任何连线，包括指向recall节点的连线
                    if (nodes.containsKey(sourceId) && nodes.containsKey(targetId)) {
                        adjacencyList.get(sourceId).add(targetId);
                        inDegree.put(targetId, inDegree.get(targetId) + 1);
                    }
                }
            }
        }

        System.out.println("   ✅ 图结构构建完成");
        System.out.println("      - 节点数量: " + nodes.size());
        System.out.println("      - 连线数量: " + connections.size());
        System.out.println("      - 有效连线: " + adjacencyList.values().stream().mapToInt(List::size).sum());
    }

    /**
     * 计算节点层级（参考SQL存储过程的逻辑）
     * 完全按照ProcessModelHierarchicalLayout_Fixed.sql中的算法实现
     */
    private void calculateNodeLevels() {
        System.out.println("   🎯 正在计算节点层级（参考SQL逻辑）...");

        // 1. 初始化所有节点层级为0
        for (FlowNode node : nodes.values()) {
            node.setLevel(0);
        }

        // 2. 找到开始节点（nodeType = 1）作为层级计算的起点
        String startNodeId = null;
        for (Map.Entry<String, FlowNode> entry : nodes.entrySet()) {
            FlowNode node = entry.getValue();
            if (node.getNodeType() != null && node.getNodeType() == 1) {
                startNodeId = entry.getKey();
                break;
            }
        }

        if (startNodeId == null) {
            System.err.println("      ❌ 未找到开始节点 (nodeType = 1)");
            return;
        }

        System.out.println("      🚀 找到开始节点: " + startNodeId);

        // 3. 使用循环方式计算节点层级（模拟SQL中的循环逻辑）
        Map<String, Integer> nodeLevels = new HashMap<>();
        Map<String, Boolean> processed = new HashMap<>();

        // 初始化：设置开始节点为第1层
        nodes.get(startNodeId).setLevel(1);
        nodeLevels.put(startNodeId, 1);
        processed.put(startNodeId, false);

        // 循环计算层级
        int currentLevel = 1;
        int maxIterations = 20; // 防止无限循环

        while (currentLevel <= maxIterations) {
            // 获取当前层级未处理的节点
            List<String> currentLevelNodes = new ArrayList<>();
            for (Map.Entry<String, Integer> entry : nodeLevels.entrySet()) {
                if (entry.getValue() == currentLevel && !processed.get(entry.getKey())) {
                    currentLevelNodes.add(entry.getKey());
                }
            }

            if (currentLevelNodes.isEmpty()) {
                break; // 没有当前层级的未处理节点，退出循环
            }

            // 找到下一层级的节点
            Set<String> nextLevelNodes = new HashSet<>();
            for (String currentNodeId : currentLevelNodes) {
                List<String> children = adjacencyList.get(currentNodeId);
                if (children != null) {
                    for (String childId : children) {
                        // 避免回到开始节点，但允许recall节点参与层级计算
                        if (!childId.equals(startNodeId) && nodes.containsKey(childId)) {
                            nextLevelNodes.add(childId);
                        }
                    }
                }
            }

            // 移除已经存在的节点
            nextLevelNodes.removeAll(nodeLevels.keySet());

            // 将新节点插入到层级表中
            int nextLevel = currentLevel + 1;
            boolean hasNewNodes = false;
            for (String nodeId : nextLevelNodes) {
                if (!nodeLevels.containsKey(nodeId)) {
                    nodes.get(nodeId).setLevel(nextLevel);
                    nodeLevels.put(nodeId, nextLevel);
                    processed.put(nodeId, false);
                    hasNewNodes = true;
                } else {
                    // 保留最小层级（如SQL中的LEAST函数）
                    int existingLevel = nodeLevels.get(nodeId);
                    if (nextLevel < existingLevel) {
                        nodes.get(nodeId).setLevel(nextLevel);
                        nodeLevels.put(nodeId, nextLevel);
                    }
                }
            }

            // 标记当前层级的节点为已处理
            for (String nodeId : currentLevelNodes) {
                processed.put(nodeId, true);
            }

            // 如果没有新节点被处理，退出循环
            if (!hasNewNodes) {
                break;
            }

            // 进入下一层级
            currentLevel++;
        }

        System.out.println("      📊 层级计算完成，共处理 " + currentLevel + " 个层级");

        // 4. 处理孤立节点（level_num = 0的节点）- 修复版本
        assignIsolatedNodesLevels();

        // 5. 网关节点独占层级处理
        adjustGatewayNodeLevels();

        // 6. 验证结束节点位置
        validateEndNodePositions();

        // 7. 重新组织底部区域节点排序：孤立节点 → 结束节点
        reorganizeBottomAreaNodes();

        // 8. 最终层级验证和修复
        performFinalLevelValidation();

        System.out.println("   ✅ 节点层级计算完成");
        printLevelStatistics();
    }

    /**
     * 为孤立节点分配临时层级（修复版本）
     * 临时标记孤立节点，最终排序在reorganizeBottomAreaNodes()中处理
     */
    private void assignIsolatedNodesLevels() {
        System.out.println("      🏝️ 临时标记孤立节点...");

        // 找到所有孤立节点（level = 0）
        List<FlowNode> isolatedNodes = new ArrayList<>();
        for (FlowNode node : nodes.values()) {
            if (node.getLevel() == 0) {
                isolatedNodes.add(node);
            }
        }

        if (isolatedNodes.isEmpty()) {
            System.out.println("         📋 未发现孤立节点");
            return;
        }

        System.out.println(String.format("         📋 发现 %d 个孤立节点", isolatedNodes.size()));

        // 临时分配一个特殊层级标记（998），稍后在reorganizeBottomAreaNodes中重新分配
        for (FlowNode isolatedNode : isolatedNodes) {
            isolatedNode.setLevel(998); // 临时标记为孤立节点

            System.out.println(String.format("         🏷️ 临时标记孤立节点 %s (%s)",
                isolatedNode.getId(),
                getNodeTypeDescription(isolatedNode)));
        }

        System.out.println(String.format("         ✅ 已临时标记 %d 个孤立节点，等待最终排序", isolatedNodes.size()));
    }

    /**
     * 重新组织底部区域节点排序
     * 确保正确的优先级：主流程 → 孤立节点 → 结束节点
     */
    private void reorganizeBottomAreaNodes() {
        System.out.println("   🔄 重新组织底部区域节点排序...");

        // 1. 识别主流程的最大层级
        int maxMainFlowLevel = calculateMaxMainFlowLevel();
        System.out.println(String.format("      📊 主流程最大层级: %d", maxMainFlowLevel));

        // 2. 收集孤立节点和结束节点
        List<FlowNode> isolatedNodes = new ArrayList<>();
        List<FlowNode> endNodes = new ArrayList<>();

        for (FlowNode node : nodes.values()) {
            // 孤立节点：临时标记为998的节点
            if (node.getLevel() == 998) {
                isolatedNodes.add(node);
            }
            // 结束节点：层级大于主流程且不是孤立节点的结束节点
            else if (node.getLevel() > maxMainFlowLevel && isEndNode(node)) {
                endNodes.add(node);
            }
        }

        System.out.println(String.format("      📋 发现底部区域节点: 孤立节点 %d 个, 结束节点 %d 个",
            isolatedNodes.size(), endNodes.size()));

        // 3. 重新分配层级：孤立节点优先
        int nextLevel = maxMainFlowLevel + 1;

        // 3.1 先分配孤立节点层级
        if (!isolatedNodes.isEmpty()) {
            System.out.println("      🏝️ 重新分配孤立节点层级...");

            // 按节点ID排序，确保一致性
            isolatedNodes.sort((a, b) -> a.getId().compareTo(b.getId()));

            for (int i = 0; i < isolatedNodes.size(); i++) {
                FlowNode isolatedNode = isolatedNodes.get(i);
                int newLevel = nextLevel + i;
                int originalLevel = isolatedNode.getLevel();

                isolatedNode.setLevel(newLevel);

                System.out.println(String.format("         📍 孤立节点 %s: %d -> %d",
                    isolatedNode.getId(), originalLevel, newLevel));
            }

            nextLevel += isolatedNodes.size();
        }

        // 3.2 再分配结束节点层级
        if (!endNodes.isEmpty()) {
            System.out.println("      🏁 重新分配结束节点层级...");

            // 按节点ID排序，确保一致性
            endNodes.sort((a, b) -> a.getId().compareTo(b.getId()));

            for (int i = 0; i < endNodes.size(); i++) {
                FlowNode endNode = endNodes.get(i);
                int newLevel = nextLevel + i;
                int originalLevel = endNode.getLevel();

                endNode.setLevel(newLevel);

                System.out.println(String.format("         🏁 结束节点 %s: %d -> %d (%s)",
                    endNode.getId(), originalLevel, newLevel,
                    endNode.getNodeName() != null ? endNode.getNodeName() : "无名称"));
            }
        }

        // 4. 输出重组结果
        int newMaxLevel = nodes.values().stream()
            .mapToInt(FlowNode::getLevel)
            .max()
            .orElse(0);

        System.out.println("      📈 底部区域重组统计:");
        System.out.println(String.format("         - 主流程层级范围: 1 - %d", maxMainFlowLevel));

        if (!isolatedNodes.isEmpty()) {
            int isolatedStartLevel = maxMainFlowLevel + 1;
            int isolatedEndLevel = maxMainFlowLevel + isolatedNodes.size();
            System.out.println(String.format("         - 孤立节点层级范围: %d - %d",
                isolatedStartLevel, isolatedEndLevel));
        }

        if (!endNodes.isEmpty()) {
            int endStartLevel = maxMainFlowLevel + isolatedNodes.size() + 1;
            int endEndLevel = maxMainFlowLevel + isolatedNodes.size() + endNodes.size();
            System.out.println(String.format("         - 结束节点层级范围: %d - %d",
                endStartLevel, endEndLevel));
        }

        System.out.println(String.format("         - 新的最大层级: %d", newMaxLevel));
        System.out.println("      ✅ 底部区域节点排序: 主流程 → 孤立节点 → 结束节点");
    }

    /**
     * 执行最终的层级验证和修复
     */
    private void performFinalLevelValidation() {
        System.out.println("   🔍 执行最终层级验证和修复...");

        // 1. 检查层级连续性
        validateLevelContinuity();

        // 2. 检查网关节点独占性
        validateGatewayExclusivity();

        // 3. 检查结束节点位置合理性
        validateEndNodeReasonableness();

        // 4. 输出最终层级分布
        printFinalLevelDistribution();
    }

    /**
     * 验证层级连续性
     */
    private void validateLevelContinuity() {
        System.out.println("      🔗 验证层级连续性...");

        Set<Integer> usedLevels = new HashSet<>();
        for (FlowNode node : nodes.values()) {
            if (node.getLevel() > 0) { // 包括所有正常层级，包括孤立节点的层级
                usedLevels.add(node.getLevel());
            }
        }

        List<Integer> sortedLevels = new ArrayList<>(usedLevels);
        sortedLevels.sort(Integer::compareTo);

        System.out.println("         使用的层级: " + sortedLevels);

        // 检查是否有跳跃的层级
        for (int i = 1; i < sortedLevels.size(); i++) {
            int current = sortedLevels.get(i);
            int previous = sortedLevels.get(i - 1);

            if (current - previous > 1) {
                System.out.println(String.format("         ⚠️ 发现层级跳跃: %d -> %d", previous, current));
            }
        }
    }

    /**
     * 验证网关节点独占性
     */
    private void validateGatewayExclusivity() {
        System.out.println("      🚪 验证网关节点独占性...");

        Map<Integer, List<FlowNode>> levelGroups = new HashMap<>();
        for (FlowNode node : nodes.values()) {
            if (node.getLevel() > 0) { // 包括所有层级，包括孤立节点
                levelGroups.computeIfAbsent(node.getLevel(), k -> new ArrayList<>()).add(node);
            }
        }

        for (Map.Entry<Integer, List<FlowNode>> entry : levelGroups.entrySet()) {
            int level = entry.getKey();
            List<FlowNode> nodesInLevel = entry.getValue();

            boolean hasGateway = false;
            boolean hasOthers = false;

            for (FlowNode node : nodesInLevel) {
                if (node.getNodeType() != null && node.getNodeType() == 13) {
                    hasGateway = true;
                } else {
                    hasOthers = true;
                }
            }

            if (hasGateway && hasOthers) {
                System.out.println(String.format("         ❌ 层级 %d 网关节点未独占！", level));

                // 列出该层级的所有节点
                for (FlowNode node : nodesInLevel) {
                    System.out.println(String.format("            - %s (%s)",
                        node.getId(), getNodeTypeDescription(node)));
                }
            } else if (hasGateway) {
                System.out.println(String.format("         ✅ 层级 %d 网关节点独占", level));
            }
        }
    }

    /**
     * 验证结束节点位置合理性
     */
    private void validateEndNodeReasonableness() {
        System.out.println("      🏁 验证结束节点位置合理性...");

        // 计算主流程的最大层级（排除孤立节点）
        int maxMainFlowLevel = calculateMaxMainFlowLevel();
        int maxLevel = nodes.values().stream()
            .filter(n -> n.getLevel() > 0)
            .mapToInt(FlowNode::getLevel)
            .max()
            .orElse(0);

        // 查找结束节点
        List<FlowNode> endNodes = new ArrayList<>();
        for (FlowNode node : nodes.values()) {
            if ((node.getNodeType() != null && node.getNodeType() == 3) ||
                "end".equals(node.getId())) {
                endNodes.add(node);
            }
        }

        for (FlowNode endNode : endNodes) {
            int level = endNode.getLevel();

            if (level >= 999) {
                System.out.println(String.format("         ❌ 结束节点 %s 层级异常: %d",
                    endNode.getId(), level));
            } else if (level < maxLevel - 2) {
                System.out.println(String.format("         ⚠️ 结束节点 %s 可能不在流程末端: 层级 %d (最大层级: %d)",
                    endNode.getId(), level, maxLevel));
            } else {
                System.out.println(String.format("         ✅ 结束节点 %s 位置合理: 层级 %d",
                    endNode.getId(), level));
            }
        }
    }

    /**
     * 输出最终层级分布
     */
    private void printFinalLevelDistribution() {
        System.out.println("      📊 最终层级分布:");

        Map<Integer, List<String>> levelDistribution = new HashMap<>();
        for (FlowNode node : nodes.values()) {
            if (node.getLevel() > 0) {
                String nodeDesc = String.format("%s(%s)",
                    node.getId(), getNodeTypeDescription(node));
                levelDistribution.computeIfAbsent(node.getLevel(), k -> new ArrayList<>()).add(nodeDesc);
            }
        }

        int maxMainFlowLevel = calculateMaxMainFlowLevel();

        levelDistribution.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> {
                int level = entry.getKey();
                List<String> nodeDescs = entry.getValue();

                String levelType;
                if (level <= maxMainFlowLevel) {
                    levelType = "主流程";
                } else {
                    levelType = "孤立节点";
                }

                System.out.println(String.format("         Level %d (%s): %s",
                    level, levelType, String.join(", ", nodeDescs)));
            });
    }

    /**
     * 调整网关节点层级，确保每个网关节点都独占一个层级行
     * 修复版本：正确处理多个网关节点之间的层级冲突
     */
    private void adjustGatewayNodeLevels() {
        System.out.println("   🚪 调整网关节点层级（修复多网关冲突版本）...");

        // 找到所有网关节点（nodeType=13）
        List<FlowNode> gatewayNodes = new ArrayList<>();
        for (FlowNode node : nodes.values()) {
            if (node.getNodeType() != null && node.getNodeType() == 13) {
                gatewayNodes.add(node);
            }
        }

        if (gatewayNodes.isEmpty()) {
            System.out.println("      📋 未发现网关节点");
            return;
        }

        System.out.println("      📋 发现 " + gatewayNodes.size() + " 个网关节点");

        // 输出网关节点的初始状态
        for (FlowNode gateway : gatewayNodes) {
            System.out.println(String.format("      📍 网关节点 %s: 初始层级 %d",
                gateway.getId(), gateway.getLevel()));
        }

        // 按当前层级从小到大排序网关节点，确保从前往后处理
        gatewayNodes.sort((a, b) -> Integer.compare(a.getLevel(), b.getLevel()));

        // 逐个处理网关节点，确保每个都独占层级
        for (int i = 0; i < gatewayNodes.size(); i++) {
            FlowNode currentGateway = gatewayNodes.get(i);
            int currentLevel = currentGateway.getLevel();

            System.out.println(String.format("      🔧 处理网关节点 %d/%d: %s (层级: %d)",
                i + 1, gatewayNodes.size(), currentGateway.getId(), currentLevel));

            // 检查该层级是否有其他节点（包括其他网关节点）
            List<FlowNode> conflictingNodes = findConflictingNodesAtLevel(currentLevel, currentGateway.getId());

            if (!conflictingNodes.isEmpty()) {
                System.out.println(String.format("         ⚠️ 发现层级冲突: %d个节点与网关节点共享层级 %d",
                    conflictingNodes.size(), currentLevel));

                // 输出冲突节点详情
                for (FlowNode conflictNode : conflictingNodes) {
                    String nodeType = conflictNode.getNodeType() != null && conflictNode.getNodeType() == 13 ?
                        "网关节点" : getNodeTypeDescription(conflictNode);
                    System.out.println(String.format("            - %s (%s)",
                        conflictNode.getId(), nodeType));
                }

                // 将所有冲突节点推送到后续层级
                pushConflictingNodesToNextLevels(conflictingNodes, currentLevel);

                System.out.println(String.format("         ✅ 网关节点 %s 现在独占层级 %d",
                    currentGateway.getId(), currentLevel));
            } else {
                System.out.println(String.format("         ✅ 网关节点 %s 已独占层级 %d",
                    currentGateway.getId(), currentLevel));
            }
        }

        // 最终验证：确保没有网关节点共享层级
        validateGatewayExclusivityFinal();

        System.out.println(String.format("      📊 网关节点层级调整完成，共处理 %d 个网关节点", gatewayNodes.size()));

        // 输出调整后的层级分布
        printLevelDistributionAfterAdjustment();
    }

    /**
     * 查找与指定网关节点在同一层级的冲突节点
     */
    private List<FlowNode> findConflictingNodesAtLevel(int level, String excludeNodeId) {
        List<FlowNode> conflictingNodes = new ArrayList<>();

        for (FlowNode node : nodes.values()) {
            if (node.getLevel() == level && !node.getId().equals(excludeNodeId)) {
                conflictingNodes.add(node);
            }
        }

        return conflictingNodes;
    }

    /**
     * 将冲突节点推送到后续层级
     */
    private void pushConflictingNodesToNextLevels(List<FlowNode> conflictingNodes, int originalLevel) {
        // 按节点类型排序：网关节点优先级最高，其他节点被推送
        conflictingNodes.sort((a, b) -> {
            boolean aIsGateway = a.getNodeType() != null && a.getNodeType() == 13;
            boolean bIsGateway = b.getNodeType() != null && b.getNodeType() == 13;

            if (aIsGateway && !bIsGateway) return -1;
            if (!aIsGateway && bIsGateway) return 1;
            return a.getId().compareTo(b.getId());
        });

        int nextAvailableLevel = findNextAvailableLevel(originalLevel);

        for (FlowNode conflictNode : conflictingNodes) {
            boolean isGateway = conflictNode.getNodeType() != null && conflictNode.getNodeType() == 13;

            if (isGateway) {
                // 如果是网关节点，需要找到下一个可用的独占层级
                nextAvailableLevel = findNextAvailableLevelForGateway(nextAvailableLevel);
                conflictNode.setLevel(nextAvailableLevel);

                System.out.println(String.format("         🚪 推送网关节点 %s: %d -> %d (独占)",
                    conflictNode.getId(), originalLevel, nextAvailableLevel));

                nextAvailableLevel++;
            } else {
                // 普通节点可以共享层级
                conflictNode.setLevel(nextAvailableLevel);

                System.out.println(String.format("         📤 推送普通节点 %s: %d -> %d",
                    conflictNode.getId(), originalLevel, nextAvailableLevel));
            }
        }

        // 递归调整后续可能受影响的层级
        adjustSubsequentLevels(nextAvailableLevel);
    }

    /**
     * 查找下一个可用层级
     */
    private int findNextAvailableLevel(int startLevel) {
        int level = startLevel + 1;

        while (isLevelOccupied(level)) {
            level++;
        }

        return level;
    }

    /**
     * 为网关节点查找下一个可用的独占层级
     */
    private int findNextAvailableLevelForGateway(int startLevel) {
        int level = startLevel;

        while (isLevelOccupied(level)) {
            level++;
        }

        return level;
    }

    /**
     * 检查指定层级是否被占用
     */
    private boolean isLevelOccupied(int level) {
        for (FlowNode node : nodes.values()) {
            if (node.getLevel() == level) {
                return true;
            }
        }
        return false;
    }

    /**
     * 最终验证网关节点独占性
     */
    private void validateGatewayExclusivityFinal() {
        System.out.println("      🔍 最终验证网关节点独占性...");

        List<FlowNode> gatewayNodes = nodes.values().stream()
            .filter(n -> n.getNodeType() != null && n.getNodeType() == 13)
            .collect(java.util.stream.Collectors.toList());

        boolean allExclusive = true;

        for (FlowNode gateway : gatewayNodes) {
            int level = gateway.getLevel();
            long nodesAtLevel = nodes.values().stream()
                .filter(n -> n.getLevel() == level)
                .count();

            if (nodesAtLevel > 1) {
                System.out.println(String.format("         ❌ 网关节点 %s 在层级 %d 未独占！共有 %d 个节点",
                    gateway.getId(), level, nodesAtLevel));
                allExclusive = false;
            } else {
                System.out.println(String.format("         ✅ 网关节点 %s 独占层级 %d",
                    gateway.getId(), level));
            }
        }

        if (allExclusive) {
            System.out.println("      ✅ 所有网关节点都成功独占各自的层级");
        } else {
            System.out.println("      ❌ 仍有网关节点未能独占层级，需要进一步调整");
        }
    }

    /**
     * 递归调整后续层级的节点，避免层级冲突
     */
    private void adjustSubsequentLevels(int startLevel) {
        // 获取所有需要调整的层级
        Set<Integer> levelsToAdjust = new HashSet<>();
        for (FlowNode node : nodes.values()) {
            if (node.getLevel() >= startLevel && node.getLevel() < 999) {
                levelsToAdjust.add(node.getLevel());
            }
        }

        // 按层级从大到小排序，避免重复调整
        List<Integer> sortedLevels = new ArrayList<>(levelsToAdjust);
        sortedLevels.sort(Collections.reverseOrder());

        for (int level : sortedLevels) {
            // 检查该层级是否有网关节点和其他节点的冲突
            boolean hasGateway = false;
            boolean hasOthers = false;

            for (FlowNode node : nodes.values()) {
                if (node.getLevel() == level) {
                    if (node.getNodeType() != null && node.getNodeType() == 13) {
                        hasGateway = true;
                    } else {
                        hasOthers = true;
                    }
                }
            }

            // 如果有冲突，将非网关节点推送到下一层级
            if (hasGateway && hasOthers) {
                for (FlowNode node : nodes.values()) {
                    if (node.getLevel() == level &&
                        (node.getNodeType() == null || node.getNodeType() != 13)) {
                        node.setLevel(level + 1);
                        System.out.println(String.format("         🔄 递归调整节点 %s: %d -> %d",
                            node.getId(), level, level + 1));
                    }
                }
            }
        }
    }

    /**
     * 输出调整后的层级分布
     */
    private void printLevelDistributionAfterAdjustment() {
        System.out.println("      📊 调整后的层级分布:");

        Map<Integer, List<String>> levelDistribution = new HashMap<>();
        for (FlowNode node : nodes.values()) {
            if (node.getLevel() > 0 && node.getLevel() < 999) {
                levelDistribution.computeIfAbsent(node.getLevel(), k -> new ArrayList<>()).add(
                    node.getId() + "(" + getNodeTypeDescription(node) + ")"
                );
            }
        }

        levelDistribution.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> {
                int level = entry.getKey();
                List<String> nodeDescs = entry.getValue();
                System.out.println(String.format("         Level %d: %s", level, String.join(", ", nodeDescs)));
            });
    }

    /**
     * 验证和修复结束节点位置
     * 修复版本：解决结束节点层级异常（如1001）的问题
     */
    private void validateEndNodePositions() {
        System.out.println("   🏁 验证和修复结束节点位置（修复版）...");

        // 查找结束节点候选
        List<FlowNode> endNodeCandidates = new ArrayList<>();

        // 1. 查找nodeType=3的节点
        for (FlowNode node : nodes.values()) {
            if (node.getNodeType() != null && node.getNodeType() == 3) {
                endNodeCandidates.add(node);
            }
        }

        // 2. 查找id="end"的节点
        if (nodes.containsKey("end")) {
            FlowNode endNode = nodes.get("end");
            if (!endNodeCandidates.contains(endNode)) {
                endNodeCandidates.add(endNode);
            }
        }

        // 3. 查找出度为0的节点（真正的结束节点）
        Set<String> nodesWithOutgoing = new HashSet<>();
        for (FlowConnection connection : connections.values()) {
            if (connection.isValidConnection()) {
                nodesWithOutgoing.add(connection.getSourceId());
            }
        }

        List<FlowNode> zeroOutDegreeNodes = new ArrayList<>();
        for (FlowNode node : nodes.values()) {
            if (!nodesWithOutgoing.contains(node.getId()) &&
                node.getLevel() > 0) { // 包括异常层级的节点
                zeroOutDegreeNodes.add(node);
                if (!endNodeCandidates.contains(node)) {
                    endNodeCandidates.add(node);
                }
            }
        }

        System.out.println("      📋 发现 " + endNodeCandidates.size() + " 个结束节点候选");
        System.out.println("      📋 发现 " + zeroOutDegreeNodes.size() + " 个出度为0的节点");

        // 获取当前的最大正常层级
        int maxNormalLevel = nodes.values().stream()
            .filter(n -> n.getLevel() > 0 && n.getLevel() < 999)
            .mapToInt(FlowNode::getLevel)
            .max()
            .orElse(0);

        System.out.println("      📊 当前最大正常层级: " + maxNormalLevel);

        // 修复结束节点的层级异常
        for (FlowNode endNode : endNodeCandidates) {
            int originalLevel = endNode.getLevel();

            System.out.println(String.format("      🔍 检查结束节点: %s (类型: %s, 原层级: %d)",
                endNode.getId(),
                endNode.getNodeType() != null ? endNode.getNodeType().toString() : "null",
                originalLevel));

            // 检查连接关系
            int incomingCount = 0;
            List<String> incomingNodes = new ArrayList<>();
            for (FlowConnection connection : connections.values()) {
                if (connection.isValidConnection() &&
                    endNode.getId().equals(connection.getTargetId())) {
                    incomingCount++;
                    incomingNodes.add(connection.getSourceId());
                }
            }

            System.out.println(String.format("         📊 入度: %d, 前置节点: %s",
                incomingCount, String.join(", ", incomingNodes)));

            // 修复异常层级
            if (originalLevel >= 999) {
                if (incomingCount > 0) {
                    // 有入度的节点不应该是孤立节点，重新计算其正确层级
                    int correctLevel = calculateCorrectEndNodeLevel(endNode, incomingNodes);
                    endNode.setLevel(correctLevel);

                    System.out.println(String.format("         🔧 修复异常层级: %d -> %d",
                        originalLevel, correctLevel));
                } else {
                    System.out.println("         ⚠️ 确实是孤立节点，保持特殊层级");
                }
            } else if (originalLevel < maxNormalLevel - 1) {
                // 结束节点不在流程末端，检查是否需要调整
                System.out.println(String.format("         ⚠️ 结束节点不在流程末端 (当前: %d, 最大: %d)",
                    originalLevel, maxNormalLevel));

                if (incomingCount == 0) {
                    System.out.println("         🔧 无入度，重新分类为孤立节点");
                    endNode.setLevel(999);
                }
            } else {
                System.out.println("         ✅ 层级正确");
            }
        }

        // 重新计算最大层级
        int newMaxLevel = nodes.values().stream()
            .filter(n -> n.getLevel() > 0 && n.getLevel() < 999)
            .mapToInt(FlowNode::getLevel)
            .max()
            .orElse(0);

        System.out.println(String.format("      📊 修复后最大层级: %d", newMaxLevel));
        System.out.println("      📊 结束节点验证和修复完成");
    }

    /**
     * 计算结束节点的正确层级
     */
    private int calculateCorrectEndNodeLevel(FlowNode endNode, List<String> incomingNodes) {
        if (incomingNodes.isEmpty()) {
            return 999; // 无前置节点，归类为孤立节点
        }

        // 找到所有前置节点的最大层级
        int maxPredecessorLevel = 0;
        for (String predecessorId : incomingNodes) {
            FlowNode predecessor = nodes.get(predecessorId);
            if (predecessor != null && predecessor.getLevel() > 0 && predecessor.getLevel() < 999) {
                maxPredecessorLevel = Math.max(maxPredecessorLevel, predecessor.getLevel());
            }
        }

        // 结束节点的层级应该是前置节点最大层级 + 1
        int correctLevel = maxPredecessorLevel + 1;

        System.out.println(String.format("         📐 计算结束节点层级: 前置最大层级(%d) + 1 = %d",
            maxPredecessorLevel, correctLevel));

        return correctLevel;
    }

    /**
     * 判断节点是否为孤立节点
     * 孤立节点：没有任何连接关系的节点
     */
    private boolean isIsolatedNode(FlowNode node) {
        String nodeId = node.getId();

        // 检查是否有任何连线涉及该节点
        for (FlowConnection connection : connections.values()) {
            if (connection.isValidConnection()) {
                if (nodeId.equals(connection.getSourceId()) ||
                    nodeId.equals(connection.getTargetId())) {
                    return false; // 有连接关系，不是孤立节点
                }
            }
        }

        return true; // 没有任何连接关系，是孤立节点
    }

    /**
     * 判断节点是否为结束节点
     */
    private boolean isEndNode(FlowNode node) {
        // 1. nodeType=3的节点
        if (node.getNodeType() != null && node.getNodeType() == 3) {
            return true;
        }

        // 2. id="end"的节点
        if ("end".equals(node.getId())) {
            return true;
        }

        // 3. 出度为0且有入度的节点（真正的流程终点）
        String nodeId = node.getId();
        boolean hasOutgoing = false;
        boolean hasIncoming = false;

        for (FlowConnection connection : connections.values()) {
            if (connection.isValidConnection()) {
                if (nodeId.equals(connection.getSourceId())) {
                    hasOutgoing = true;
                }
                if (nodeId.equals(connection.getTargetId())) {
                    hasIncoming = true;
                }
            }
        }

        // 出度为0且有入度，且不是孤立节点
        return !hasOutgoing && hasIncoming && !isIsolatedNode(node);
    }

    /**
     * 计算智能连线布局
     * 避免连线重叠和穿过节点中心
     */
    private void calculateConnectionLayout() {
        System.out.println("   🔗 计算智能连线布局...");
        System.out.println(String.format("      📋 发现连线总数: %d", connections.size()));

        // 统计连线信息
        int totalConnections = 0;
        int validConnections = 0;
        int processedConnections = 0;
        int skippedConnections = 0;

        for (Map.Entry<String, FlowConnection> entry : connections.entrySet()) {
            String connectionId = entry.getKey();
            FlowConnection connection = entry.getValue();
            totalConnections++;

            if (connection.isValidConnection()) {
                validConnections++;

                String sourceId = connection.getSourceId();
                String targetId = connection.getTargetId();

                System.out.println(String.format("      🔍 处理连线 %s: %s -> %s",
                    connectionId, sourceId, targetId));

                FlowNode sourceNode = nodes.get(sourceId);
                FlowNode targetNode = nodes.get(targetId);

                if (sourceNode != null && targetNode != null) {
                    if (sourceNode.getGeometry() != null && targetNode.getGeometry() != null) {
                        // 计算智能连接点
                        ConnectionPath path = calculateSmartConnectionPath(sourceNode, targetNode, connection);

                        // 更新连线的几何信息
                        updateConnectionGeometry(connection, path);

                        processedConnections++;

                        System.out.println(String.format("         ✅ 已计算路径: (%.1f,%.1f) -> (%.1f,%.1f)",
                            path.startX, path.startY, path.endX, path.endY));
                    } else {
                        skippedConnections++;
                        System.out.println(String.format("         ⚠️ 跳过：节点几何信息缺失 (源:%s, 目标:%s)",
                            sourceNode.getGeometry() == null ? "无" : "有",
                            targetNode.getGeometry() == null ? "无" : "有"));
                    }
                } else {
                    skippedConnections++;
                    System.out.println(String.format("         ⚠️ 跳过：节点不存在 (源:%s, 目标:%s)",
                        sourceNode == null ? "不存在" : "存在",
                        targetNode == null ? "不存在" : "存在"));
                }
            } else {
                skippedConnections++;
                System.out.println(String.format("      ⚠️ 跳过无效连线 %s", connectionId));
            }
        }

        System.out.println(String.format("      📊 连线布局统计:"));
        System.out.println(String.format("         - 总连线数: %d", totalConnections));
        System.out.println(String.format("         - 有效连线: %d", validConnections));
        System.out.println(String.format("         - 已处理: %d", processedConnections));
        System.out.println(String.format("         - 跳过: %d", skippedConnections));
        System.out.println("   ✅ 智能连线布局计算完成");
    }

    /**
     * 计算智能连接路径
     */
    private ConnectionPath calculateSmartConnectionPath(FlowNode sourceNode, FlowNode targetNode, FlowConnection connection) {
        ConnectionPath path = new ConnectionPath();

        // 获取节点的几何信息
        FlowNode.Geometry sourceGeom = sourceNode.getGeometry();
        FlowNode.Geometry targetGeom = targetNode.getGeometry();

        // 计算节点的边界
        NodeBounds sourceBounds = calculateNodeBounds(sourceGeom);
        NodeBounds targetBounds = calculateNodeBounds(targetGeom);

        // 判断连线类型和方向
        ConnectionType connectionType = determineConnectionType(sourceNode, targetNode);

        // 根据连线类型计算连接点
        switch (connectionType) {
            case HORIZONTAL_SAME_LEVEL:
                path = calculateHorizontalPath(sourceBounds, targetBounds, connection);
                break;
            case VERTICAL_CROSS_LEVEL:
                path = calculateVerticalPath(sourceBounds, targetBounds, connection);
                break;
            case DIAGONAL:
                path = calculateDiagonalPath(sourceBounds, targetBounds, connection);
                break;
            default:
                path = calculateDefaultPath(sourceBounds, targetBounds, connection);
                break;
        }

        System.out.println(String.format("      🔗 连线 %s -> %s: %s路径",
            sourceNode.getId(), targetNode.getId(), connectionType.getDescription()));

        return path;
    }

    /**
     * 计算节点边界
     */
    private NodeBounds calculateNodeBounds(FlowNode.Geometry geometry) {
        NodeBounds bounds = new NodeBounds();

        // 默认节点尺寸（如果JSON中没有指定）
        double defaultWidth = 120;
        double defaultHeight = 60;

        bounds.centerX = geometry.getX();
        bounds.centerY = geometry.getY();
        bounds.width = geometry.getWidth() > 0 ? geometry.getWidth() : defaultWidth;
        bounds.height = geometry.getHeight() > 0 ? geometry.getHeight() : defaultHeight;

        // 计算边界坐标
        bounds.left = bounds.centerX - bounds.width / 2;
        bounds.right = bounds.centerX + bounds.width / 2;
        bounds.top = bounds.centerY - bounds.height / 2;
        bounds.bottom = bounds.centerY + bounds.height / 2;

        return bounds;
    }

    /**
     * 确定连线类型
     */
    private ConnectionType determineConnectionType(FlowNode sourceNode, FlowNode targetNode) {
        int sourceLevel = sourceNode.getLevel();
        int targetLevel = targetNode.getLevel();

        double sourceX = sourceNode.getGeometry().getX();
        double targetX = targetNode.getGeometry().getX();
        double sourceY = sourceNode.getGeometry().getY();
        double targetY = targetNode.getGeometry().getY();

        // 同层级连线
        if (sourceLevel == targetLevel) {
            return ConnectionType.HORIZONTAL_SAME_LEVEL;
        }

        // 垂直跨层级连线
        if (Math.abs(sourceX - targetX) < 100) { // X坐标相近
            return ConnectionType.VERTICAL_CROSS_LEVEL;
        }

        // 对角线连线
        return ConnectionType.DIAGONAL;
    }

    /**
     * 计算水平路径（同层级节点）
     */
    private ConnectionPath calculateHorizontalPath(NodeBounds source, NodeBounds target, FlowConnection connection) {
        ConnectionPath path = new ConnectionPath();

        // 计算连接点偏移（避免多条连线重叠）
        int connectionIndex = getConnectionIndex(connection);
        double yOffset = connectionIndex * 15; // 每条连线间隔15像素

        // 起点：源节点右边缘
        path.startX = source.right;
        path.startY = source.centerY + yOffset;

        // 终点：目标节点左边缘
        path.endX = target.left;
        path.endY = target.centerY + yOffset;

        // 添加中间控制点（弯曲路径）
        double midX = (path.startX + path.endX) / 2;
        path.addControlPoint(midX, path.startY);
        path.addControlPoint(midX, path.endY);

        return path;
    }

    /**
     * 计算垂直路径（跨层级节点）
     */
    private ConnectionPath calculateVerticalPath(NodeBounds source, NodeBounds target, FlowConnection connection) {
        ConnectionPath path = new ConnectionPath();

        // 计算连接点偏移
        int connectionIndex = getConnectionIndex(connection);
        double xOffset = connectionIndex * 20; // 每条连线间隔20像素

        // 起点：源节点下边缘
        path.startX = source.centerX + xOffset;
        path.startY = source.bottom;

        // 终点：目标节点上边缘
        path.endX = target.centerX + xOffset;
        path.endY = target.top;

        // 添加中间控制点
        double midY = (path.startY + path.endY) / 2;
        path.addControlPoint(path.startX, midY);
        path.addControlPoint(path.endX, midY);

        return path;
    }

    /**
     * 计算对角线路径
     */
    private ConnectionPath calculateDiagonalPath(NodeBounds source, NodeBounds target, FlowConnection connection) {
        ConnectionPath path = new ConnectionPath();

        // 计算最佳连接点
        ConnectionPoint sourcePoint = calculateBestConnectionPoint(source, target, true);
        ConnectionPoint targetPoint = calculateBestConnectionPoint(target, source, false);

        path.startX = sourcePoint.x;
        path.startY = sourcePoint.y;
        path.endX = targetPoint.x;
        path.endY = targetPoint.y;

        // 添加弯曲控制点，避免直线穿过其他节点
        addAvoidanceControlPoints(path, source, target);

        return path;
    }

    /**
     * 计算默认路径
     */
    private ConnectionPath calculateDefaultPath(NodeBounds source, NodeBounds target, FlowConnection connection) {
        ConnectionPath path = new ConnectionPath();

        // 简单的直线连接
        path.startX = source.centerX;
        path.startY = source.centerY;
        path.endX = target.centerX;
        path.endY = target.centerY;

        return path;
    }

    /**
     * 计算最佳连接点
     */
    private ConnectionPoint calculateBestConnectionPoint(NodeBounds nodeBounds, NodeBounds otherBounds, boolean isSource) {
        ConnectionPoint point = new ConnectionPoint();

        // 计算方向向量
        double dx = otherBounds.centerX - nodeBounds.centerX;
        double dy = otherBounds.centerY - nodeBounds.centerY;

        // 根据方向选择连接点
        if (Math.abs(dx) > Math.abs(dy)) {
            // 水平方向为主
            if (dx > 0) {
                // 目标在右侧，使用右边缘
                point.x = nodeBounds.right;
                point.y = nodeBounds.centerY;
            } else {
                // 目标在左侧，使用左边缘
                point.x = nodeBounds.left;
                point.y = nodeBounds.centerY;
            }
        } else {
            // 垂直方向为主
            if (dy > 0) {
                // 目标在下方，使用下边缘
                point.x = nodeBounds.centerX;
                point.y = nodeBounds.bottom;
            } else {
                // 目标在上方，使用上边缘
                point.x = nodeBounds.centerX;
                point.y = nodeBounds.top;
            }
        }

        return point;
    }

    /**
     * 添加避让控制点
     */
    private void addAvoidanceControlPoints(ConnectionPath path, NodeBounds source, NodeBounds target) {
        // 简化版本：添加一个中间控制点
        double midX = (path.startX + path.endX) / 2;
        double midY = (path.startY + path.endY) / 2;

        // 向外偏移，避免穿过节点
        double offsetX = 50;
        double offsetY = 30;

        if (path.startX < path.endX) {
            midX += offsetX;
        } else {
            midX -= offsetX;
        }

        if (path.startY < path.endY) {
            midY += offsetY;
        } else {
            midY -= offsetY;
        }

        path.addControlPoint(midX, midY);
    }

    /**
     * 获取连线索引（用于计算偏移）
     */
    private int getConnectionIndex(FlowConnection connection) {
        // 简化版本：基于连线ID的哈希值
        return Math.abs(connection.getId().hashCode()) % 5;
    }

    /**
     * 更新连线的几何信息（mxGraph格式）
     */
    private void updateConnectionGeometry(FlowConnection connection, ConnectionPath path) {
        // 创建mxGraph格式的几何信息
        MxGraphConnectionGeometry mxGeometry;

        if (path.controlPoints.isEmpty()) {
            // 简单的两点连线
            mxGeometry = MxGraphConnectionGeometry.createSimpleLine(
                path.startX, path.startY, path.endX, path.endY);
        } else {
            // 带中间点的避让路径
            mxGeometry = MxGraphConnectionGeometry.createAvoidancePath(
                path.startX, path.startY, path.endX, path.endY, path.controlPoints);
        }

        // 将mxGraph几何信息存储到连线对象中
        // 注意：这里我们需要一个方法来设置mxGraph几何信息
        setMxGraphGeometry(connection, mxGeometry);
    }

    /**
     * 设置连线的mxGraph几何信息
     */
    private void setMxGraphGeometry(FlowConnection connection, MxGraphConnectionGeometry mxGeometry) {
        // 由于FlowConnection.Geometry还是旧格式，我们需要临时存储mxGraph几何信息
        // 在JSON输出时会使用这个信息
        connection.setMxGraphGeometry(mxGeometry);
    }

    // 内部数据结构

    /**
     * 连线类型枚举
     */
    private enum ConnectionType {
        HORIZONTAL_SAME_LEVEL("水平同层"),
        VERTICAL_CROSS_LEVEL("垂直跨层"),
        DIAGONAL("对角线"),
        DEFAULT("默认");

        private final String description;

        ConnectionType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 节点边界信息
     */
    private static class NodeBounds {
        double centerX, centerY;
        double width, height;
        double left, right, top, bottom;
    }

    /**
     * 连接点信息
     */
    public static class ConnectionPoint {
        public double x, y;
    }

    /**
     * 连接路径信息
     */
    private static class ConnectionPath {
        double startX, startY;
        double endX, endY;
        List<ConnectionPoint> controlPoints = new ArrayList<>();
        String pathType = "curved";

        void addControlPoint(double x, double y) {
            ConnectionPoint point = new ConnectionPoint();
            point.x = x;
            point.y = y;
            controlPoints.add(point);
        }
    }

    /**
     * 计算主流程的最大层级（排除孤立节点和结束节点）
     */
    private int calculateMaxMainFlowLevel() {
        return nodes.values().stream()
            .filter(n -> n.getLevel() > 0 &&
                        n.getLevel() != 998 && // 排除临时标记的孤立节点
                        !isEndNode(n))         // 排除结束节点
            .mapToInt(FlowNode::getLevel)
            .max()
            .orElse(0);
    }

    /**
     * 计算每个层级内的节点排序（参考SQL逻辑，增强网关节点和结束节点处理）
     * 对应SQL中第283-304行的逻辑
     */
    private void calculateLevelOrders() {
        System.out.println("   📋 正在计算层级内节点排序（增强版）...");

        // 按层级分组节点（包括孤立节点）
        Map<Integer, List<FlowNode>> levelGroups = new HashMap<>();
        for (FlowNode node : nodes.values()) {
            if (node.getLevel() > 0) { // 处理所有已分配层级的节点，包括孤立节点
                levelGroups.computeIfAbsent(node.getLevel(), k -> new ArrayList<>()).add(node);
            }
        }

        // 为每个层级内的节点分配排序号
        for (Map.Entry<Integer, List<FlowNode>> entry : levelGroups.entrySet()) {
            int level = entry.getKey();
            List<FlowNode> nodesInLevel = entry.getValue();

            // 增强排序逻辑：优先级排序 + ID排序
            nodesInLevel.sort((a, b) -> {
                // 1. 网关节点优先级最高（排在前面）
                boolean aIsGateway = a.getNodeType() != null && a.getNodeType() == 13;
                boolean bIsGateway = b.getNodeType() != null && b.getNodeType() == 13;

                if (aIsGateway && !bIsGateway) return -1;
                if (!aIsGateway && bIsGateway) return 1;

                // 2. 开始节点优先级第二
                boolean aIsStart = a.getNodeType() != null && a.getNodeType() == 1;
                boolean bIsStart = b.getNodeType() != null && b.getNodeType() == 1;

                if (aIsStart && !bIsStart) return -1;
                if (!aIsStart && bIsStart) return 1;

                // 3. 结束节点优先级较低（排在后面）
                boolean aIsEnd = (a.getNodeType() != null && a.getNodeType() == 3) || "end".equals(a.getId());
                boolean bIsEnd = (b.getNodeType() != null && b.getNodeType() == 3) || "end".equals(b.getId());

                if (aIsEnd && !bIsEnd) return 1;
                if (!aIsEnd && bIsEnd) return -1;

                // 4. 最后按ID排序（对应SQL中的ORDER BY id）
                return a.getId().compareTo(b.getId());
            });

            // 分配levelOrder（对应SQL中的ROW_NUMBER() OVER (PARTITION BY level_num ORDER BY id)）
            for (int i = 0; i < nodesInLevel.size(); i++) {
                FlowNode node = nodesInLevel.get(i);
                node.setLevelOrder(i + 1);

                // 输出详细信息
                String nodeTypeDesc = getNodeTypeDescription(node);
                System.out.println(String.format("      Level %d, Order %d: %s (%s)",
                    level, i + 1, node.getId(), nodeTypeDesc));
            }

            System.out.println(String.format("      Level %d: %d个节点排序完成", level, nodesInLevel.size()));
        }

        System.out.println("   ✅ 层级内排序计算完成");
    }

    /**
     * 获取节点类型描述
     */
    private String getNodeTypeDescription(FlowNode node) {
        if (node.getNodeType() == null) {
            return "未知类型";
        }

        switch (node.getNodeType()) {
            case 1: return "开始节点";
            case 2: return "初始化节点";
            case 3: return "结束节点";
            case 4: return "重新提交节点";
            case 6: return "审核节点";
            case 8: return "循环节点";
            case 9: return "服务节点";
            case 13: return "网关节点";
            default: return "类型" + node.getNodeType();
        }
    }

    /**
     * 计算节点坐标（参考SQL中的坐标计算逻辑，增强网关节点处理）
     * 对应SQL中第306-371行的逻辑
     */
    private void calculateNodeCoordinates() {
        System.out.println("   📐 正在计算节点坐标（参考SQL逻辑，增强网关节点处理）...");

        // 先计算层级内排序
        calculateLevelOrders();

        // 使用SQL中的坐标计算公式，所有节点统一处理（修复版本）
        for (FlowNode node : nodes.values()) {
            if (node.getLevel() > 0) {
                // 所有节点（包括孤立节点）都使用标准的坐标计算公式
                int level = node.getLevel();
                int levelOrder = node.getLevelOrder();

                double x = 150 + (levelOrder - 1) * 500;  // 横向间隔减半：500 -> 250
                double y = 150 + (level - 1) * 200;       // 纵向间隔减半：400 -> 200

                // 网关节点特殊处理：增加额外的垂直间距
                if (node.getNodeType() != null && node.getNodeType() == 13) {
                    y += 50; // 网关节点额外向下偏移50像素，增强独占效果

                    // 网关节点在层级内居中
                    List<FlowNode> sameLevel = nodes.values().stream()
                        .filter(n -> n.getLevel() == level)
                        .collect(java.util.stream.Collectors.toList());

                    if (sameLevel.size() == 1) {
                        // 如果该层级只有网关节点，居中显示
                        x = 150 + 2 * 250; // 居中位置（间隔减半）
                    }

                    System.out.println(String.format("      🚪 网关节点 %s 坐标: (%.0f, %.0f)",
                        node.getId(), x, y));
                }

                // 孤立节点特殊标记（用于调试）
                if (isIsolatedNode(node)) {
                    System.out.println(String.format("      🏝️ 孤立节点 %s 坐标: (%.0f, %.0f) [层级 %d]",
                        node.getId(), x, y, level));
                }

                updateNodeGeometry(node, x, y);
            }
        }

        // 特殊处理：重新提交节点坐标调整（对应SQL第318-342行）
        handleSpecialNodeCoordinates();

        // 验证结束节点坐标
        validateEndNodeCoordinates();

        // 计算智能连线布局
        calculateConnectionLayout();

        System.out.println("   ✅ 节点坐标和连线布局计算完成");
    }

    /**
     * 验证结束节点坐标是否正确
     */
    private void validateEndNodeCoordinates() {
        System.out.println("   🏁 验证结束节点坐标...");

        // 查找结束节点
        List<FlowNode> endNodes = new ArrayList<>();
        for (FlowNode node : nodes.values()) {
            if ((node.getNodeType() != null && node.getNodeType() == 3) ||
                "end".equals(node.getId())) {
                endNodes.add(node);
            }
        }

        // 查找出度为0的节点
        Set<String> nodesWithOutgoing = new HashSet<>();
        for (FlowConnection connection : connections.values()) {
            if (connection.isValidConnection()) {
                nodesWithOutgoing.add(connection.getSourceId());
            }
        }

        for (FlowNode node : nodes.values()) {
            if (!nodesWithOutgoing.contains(node.getId()) &&
                node.getLevel() > 0 && node.getLevel() < 999 &&
                !endNodes.contains(node)) {
                endNodes.add(node);
            }
        }

        for (FlowNode endNode : endNodes) {
            if (endNode.getGeometry() != null) {
                System.out.println(String.format("      🏁 结束节点 %s: 层级=%d, 排序=%d, 坐标=(%.0f, %.0f)",
                    endNode.getId(),
                    endNode.getLevel(),
                    endNode.getLevelOrder(),
                    endNode.getGeometry().getX(),
                    endNode.getGeometry().getY()));

                // 验证坐标计算是否正确
                double expectedX = 150 + (endNode.getLevelOrder() - 1) * 500;
                double expectedY = 150 + (endNode.getLevel() - 1) * 400;

                if (Math.abs(endNode.getGeometry().getX() - expectedX) > 1 ||
                    Math.abs(endNode.getGeometry().getY() - expectedY) > 1) {
                    System.out.println(String.format("         ⚠️ 坐标异常！预期: (%.0f, %.0f)", expectedX, expectedY));
                } else {
                    System.out.println("         ✅ 坐标正确");
                }
            }
        }
    }

    /**
     * 处理特殊节点坐标（对应SQL中第318-342行的逻辑）
     */
    private void handleSpecialNodeCoordinates() {
        System.out.println("   ⚡ 处理特殊节点坐标...");

        // 获取byStart节点的坐标
        FlowNode byStartNode = nodes.get("byStart");
        double byStartX = 2600; // 默认坐标
        double byStartY = 940;  // 默认坐标

        if (byStartNode != null && byStartNode.getGeometry() != null) {
            byStartX = byStartNode.getGeometry().getX();
            byStartY = byStartNode.getGeometry().getY();
        }

        // 更新重新提交节点的坐标（nodeType = 4）
        int recallNodeCount = 0;
        for (FlowNode node : nodes.values()) {
            if (node.getNodeType() != null && node.getNodeType() == 4) {
                // recall节点坐标：byStart节点左侧1000像素，相同Y坐标
                double recallX = byStartX - 1000;
                double recallY = byStartY;

                updateNodeGeometry(node, recallX, recallY);
                recallNodeCount++;

                System.out.println(String.format("      📍 更新recall节点 %s 坐标: (%.0f, %.0f)",
                    node.getId(), recallX, recallY));
            }
        }

        System.out.println(String.format("   ✅ 特殊节点坐标处理完成，处理了 %d 个recall节点", recallNodeCount));
    }

    /**
     * 更新节点几何信息
     */
    private void updateNodeGeometry(FlowNode node, double x, double y) {
        if (node.getGeometry() == null) {
            node.setGeometry(new FlowNode.Geometry(x, y, NODE_WIDTH, NODE_HEIGHT));
        } else {
            node.getGeometry().setX(x);
            node.getGeometry().setY(y);
            // 保持原有的宽度和高度，或使用默认值
            if (node.getGeometry().getWidth() == 0) {
                node.getGeometry().setWidth(NODE_WIDTH);
            }
            if (node.getGeometry().getHeight() == 0) {
                node.getGeometry().setHeight(NODE_HEIGHT);
            }
        }
    }

    /**
     * 更新节点几何信息（包含自定义宽度和高度）
     */
    private void updateNodeGeometry(FlowNode node, double x, double y, double width, double height) {
        if (node.getGeometry() == null) {
            node.setGeometry(new FlowNode.Geometry(x, y, width, height));
        } else {
            node.getGeometry().setX(x);
            node.getGeometry().setY(y);
            node.getGeometry().setWidth(width);
            node.getGeometry().setHeight(height);
        }
    }

    /**
     * 更新JSON并输出到文件
     */
    private void updateJsonWithNewLayout(JsonNode rootNode, String outputFilePath) throws IOException {
        System.out.println("   💾 正在更新JSON数据...");

        ObjectNode outputRoot = (ObjectNode) rootNode.deepCopy();

        // 更新所有节点的坐标和层级信息
        for (Map.Entry<String, FlowNode> entry : nodes.entrySet()) {
            String nodeId = entry.getKey();
            FlowNode node = entry.getValue();

            JsonNode nodeJson = outputRoot.get(nodeId);
            if (nodeJson != null && nodeJson.isObject()) {
                ObjectNode nodeObject = (ObjectNode) nodeJson;

                // 更新geometry信息
                if (node.getGeometry() != null) {
                    ObjectNode geometryNode = objectMapper.createObjectNode();
                    geometryNode.put("x", node.getGeometry().getX());
                    geometryNode.put("y", node.getGeometry().getY());
                    geometryNode.put("width", node.getGeometry().getWidth());
                    geometryNode.put("height", node.getGeometry().getHeight());
                    geometryNode.put("TRANSLATE_CONTROL_POINTS", "true");

                    nodeObject.set("geometry", geometryNode);
                }

                // 添加层级信息
                nodeObject.put("level_num", node.getLevel());
                nodeObject.put("level_num_order", node.getLevelOrder());
            }
        }

        // 更新所有连线的mxGraph几何信息
        for (Map.Entry<String, FlowConnection> entry : connections.entrySet()) {
            String connectionId = entry.getKey();
            FlowConnection connection = entry.getValue();

            JsonNode connectionJson = outputRoot.get(connectionId);
            if (connectionJson != null && connectionJson.isObject()) {
                ObjectNode connectionObject = (ObjectNode) connectionJson;

                // 更新连线的mxGraph几何信息
                if (connection.getMxGraphGeometry() != null) {
                    ObjectNode geometryNode = objectMapper.createObjectNode();
                    MxGraphConnectionGeometry mxGeometry = connection.getMxGraphGeometry();

                    // 设置mxGraph标准字段
                    geometryNode.put("x", mxGeometry.getX());
                    geometryNode.put("y", mxGeometry.getY());
                    geometryNode.put("width", mxGeometry.getWidth());
                    geometryNode.put("height", mxGeometry.getHeight());
                    geometryNode.put("relative", mxGeometry.getRelative());

                    // 添加abspoints数组
                    if (mxGeometry.getAbspoints() != null && !mxGeometry.getAbspoints().isEmpty()) {
                        ArrayNode abspointsArray = objectMapper.createArrayNode();
                        for (MxGraphConnectionGeometry.AbsPoint point : mxGeometry.getAbspoints()) {
                            ObjectNode pointNode = objectMapper.createObjectNode();
                            pointNode.put("x", point.getX());
                            pointNode.put("y", point.getY());
                            abspointsArray.add(pointNode);
                        }
                        geometryNode.set("abspoints", abspointsArray);
                    }

                    connectionObject.set("geometry", geometryNode);

                    System.out.println(String.format("      🔗 更新连线 %s mxGraph几何信息: %d个路径点",
                        connectionId, mxGeometry.getAbspoints() != null ? mxGeometry.getAbspoints().size() : 0));
                }
            }
        }

        // 写入输出文件
        try (FileWriter writer = new FileWriter(outputFilePath)) {
            String jsonString = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(outputRoot);
            writer.write(jsonString);
        }

        System.out.println("   ✅ JSON文件已保存到: " + outputFilePath);
    }

    /**
     * 解析流程节点
     */
    private FlowNode parseFlowNode(String id, JsonNode nodeJson) {
        FlowNode node = new FlowNode();
        node.setId(id);

        if (nodeJson.has("nodeName")) {
            node.setNodeName(nodeJson.get("nodeName").asText());
        }

        if (nodeJson.has("nodeType")) {
            node.setNodeType(nodeJson.get("nodeType").asInt());
        }

        if (nodeJson.has("geometry")) {
            JsonNode geometryNode = nodeJson.get("geometry");
            FlowNode.Geometry geometry = new FlowNode.Geometry();

            if (geometryNode.has("x")) {
                geometry.setX(geometryNode.get("x").asDouble());
            }
            if (geometryNode.has("y")) {
                geometry.setY(geometryNode.get("y").asDouble());
            }
            if (geometryNode.has("width")) {
                geometry.setWidth(geometryNode.get("width").asDouble());
            }
            if (geometryNode.has("height")) {
                geometry.setHeight(geometryNode.get("height").asDouble());
            }

            node.setGeometry(geometry);
        }

        return node;
    }

    /**
     * 解析流程连线
     */
    private FlowConnection parseFlowConnection(String id, JsonNode connectionJson) {
        FlowConnection connection = new FlowConnection();
        connection.setId(id);

        if (connectionJson.has("nodeType")) {
            connection.setNodeType(connectionJson.get("nodeType").asInt());
        }

        if (connectionJson.has("value")) {
            connection.setValue(connectionJson.get("value").asText());
        }

        if (connectionJson.has("source")) {
            JsonNode sourceNode = connectionJson.get("source");
            FlowConnection.NodeReference source = new FlowConnection.NodeReference();

            if (sourceNode.has("id")) {
                source.setId(sourceNode.get("id").asText());
            }
            if (sourceNode.has("nodeName")) {
                source.setNodeName(sourceNode.get("nodeName").asText());
            }
            if (sourceNode.has("nodeType")) {
                source.setNodeType(sourceNode.get("nodeType").asInt());
            }

            connection.setSource(source);
        }

        if (connectionJson.has("target")) {
            JsonNode targetNode = connectionJson.get("target");
            FlowConnection.NodeReference target = new FlowConnection.NodeReference();

            if (targetNode.has("id")) {
                target.setId(targetNode.get("id").asText());
            }
            if (targetNode.has("nodeName")) {
                target.setNodeName(targetNode.get("nodeName").asText());
            }
            if (targetNode.has("nodeType")) {
                target.setNodeType(targetNode.get("nodeType").asInt());
            }

            connection.setTarget(target);
        }

        return connection;
    }

    /**
     * 判断是否为节点对象
     */
    private boolean isNodeObject(JsonNode value) {
        return value.has("nodeType") &&
               value.has("geometry") &&
               (!value.has("edge") || !"true".equals(value.get("edge").asText()));
    }

    /**
     * 判断是否为连线对象
     */
    private boolean isConnectionObject(JsonNode value) {
        return (value.has("edge") && "true".equals(value.get("edge").asText())) ||
               (value.has("source") && value.has("target"));
    }

    /**
     * 获取节点类型名称
     */
    private String getNodeTypeName(int nodeType) {
        switch (nodeType) {
            case 1: return "开始节点";
            case 2: return "初始化节点";
            case 4: return "重新提交节点";
            case 6: return "审核节点";
            case 8: return "循环节点";
            case 9: return "服务节点";
            case 13: return "网关节点";
            case 99: return "连线";
            default: return "未知类型";
        }
    }

    /**
     * 打印层级统计信息
     */
    private void printLevelStatistics() {
        Map<Integer, Long> levelCount = nodes.values().stream()
            .collect(Collectors.groupingBy(FlowNode::getLevel, Collectors.counting()));

        System.out.println("   📊 层级分布统计:");
        levelCount.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> {
                int level = entry.getKey();
                long count = entry.getValue();
                if (level == 999) {
                    System.out.println(String.format("      特殊/孤立节点: %d个", count));
                } else {
                    System.out.println(String.format("      Level %d: %d个节点", level, count));
                }
            });
    }

    /**
     * 打印布局统计信息
     */
    private void printLayoutStatistics() {
        System.out.println("\n📈 布局处理统计:");
        System.out.println("   - 处理节点总数: " + nodes.size());
        System.out.println("   - 处理连线总数: " + connections.size());

        long normalNodes = nodes.values().stream()
            .filter(node -> !node.isSpecialNode() && node.getLevel() < 999)
            .count();
        long specialNodes = nodes.values().stream()
            .filter(FlowNode::isSpecialNode)
            .count();
        long isolatedNodes = nodes.values().stream()
            .filter(node -> !node.isSpecialNode() && node.getLevel() == 999)
            .count();

        System.out.println("   - 正常布局节点: " + normalNodes);
        System.out.println("   - 特殊节点: " + specialNodes);
        System.out.println("   - 孤立节点: " + isolatedNodes);

        int maxLevel = nodes.values().stream()
            .filter(node -> node.getLevel() < 999)
            .mapToInt(FlowNode::getLevel)
            .max()
            .orElse(0);

        System.out.println("   - 最大层级: " + maxLevel);

        // 计算布局范围
        double minX = nodes.values().stream()
            .filter(node -> node.getGeometry() != null)
            .mapToDouble(node -> node.getGeometry().getX())
            .min()
            .orElse(0);

        double maxX = nodes.values().stream()
            .filter(node -> node.getGeometry() != null)
            .mapToDouble(node -> node.getGeometry().getX())
            .max()
            .orElse(0);

        double minY = nodes.values().stream()
            .filter(node -> node.getGeometry() != null)
            .mapToDouble(node -> node.getGeometry().getY())
            .min()
            .orElse(0);

        double maxY = nodes.values().stream()
            .filter(node -> node.getGeometry() != null)
            .mapToDouble(node -> node.getGeometry().getY())
            .max()
            .orElse(0);

        System.out.println(String.format("   - 布局范围: X[%.0f, %.0f], Y[%.0f, %.0f]",
            minX, maxX, minY, maxY));
    }

    /**
     * 使用简单连接关系检测真正的孤立节点
     * 这个方法类似于NodeConnectionAnalyzer的逻辑
     */
    private Set<String> findTrulyIsolatedNodes() {
        Set<String> connectedNodes = new HashSet<>();

        // 遍历所有连线，找出有连接关系的节点
        for (FlowConnection connection : connections.values()) {
            if (connection.isValidConnection()) {
                String sourceId = connection.getSourceId();
                String targetId = connection.getTargetId();

                if (nodes.containsKey(sourceId)) {
                    connectedNodes.add(sourceId);
                }
                if (nodes.containsKey(targetId)) {
                    connectedNodes.add(targetId);
                }
            }
        }

        // 找出孤立节点
        Set<String> isolatedNodes = new HashSet<>();
        for (String nodeId : nodes.keySet()) {
            if (!connectedNodes.contains(nodeId)) {
                isolatedNodes.add(nodeId);
            }
        }

        return isolatedNodes;
    }

}